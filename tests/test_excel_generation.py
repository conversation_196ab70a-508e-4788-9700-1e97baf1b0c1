#!/usr/bin/env python3
"""
Test script to verify Excel generation functionality
"""

import requests
import json
import sys
import os

def test_database_debug():
    """Test the database debug endpoint"""
    try:
        response = requests.post('http://localhost:8080/api/debug-db', 
                               headers={'Content-Type': 'application/json'},
                               json={})
        
        if response.ok:
            data = response.json()
            print("Database Debug Info:")
            print(f"  Database path: {data.get('database_path', 'Unknown')}")
            print(f"  Tables: {data.get('tables', [])}")
            print(f"  Patent analysis records: {data.get('patent_analysis_count', 0)}")
            print(f"  Infringed models records: {data.get('infringed_models_count', 0)}")
            print(f"  Sample patents: {data.get('sample_patents', [])}")
            return data.get('sample_patents', [])
        else:
            print(f"Database debug failed: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error testing database: {e}")
        return []

def test_excel_download():
    """Test the Excel download endpoint"""
    try:
        response = requests.get('http://localhost:8080/download-report')
        
        if response.ok:
            print("Excel download successful!")
            print(f"Content type: {response.headers.get('Content-Type')}")
            print(f"Content disposition: {response.headers.get('Content-Disposition')}")
            print(f"Content length: {len(response.content)} bytes")
            
            # Save the file for verification
            with open('test_downloaded_report.xlsx', 'wb') as f:
                f.write(response.content)
            print("Excel file saved as 'test_downloaded_report.xlsx'")
            return True
        else:
            print(f"Excel download failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"Error testing Excel download: {e}")
        return False

def main():
    print("Testing Excel generation functionality...")
    print("=" * 50)
    
    # Test database
    print("\n1. Testing database debug endpoint...")
    sample_patents = test_database_debug()
    
    # Test Excel download
    print("\n2. Testing Excel download endpoint...")
    excel_success = test_excel_download()
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"  Database has patents: {len(sample_patents) > 0}")
    print(f"  Excel generation works: {excel_success}")
    
    if len(sample_patents) > 0:
        print(f"  Sample patents found: {sample_patents}")
    else:
        print("  No patents found in database - you may need to run an analysis first")

if __name__ == "__main__":
    main()
