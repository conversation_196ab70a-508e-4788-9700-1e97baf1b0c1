<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Table Formatting Test</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container mt-4">
    <h2>Table Formatting Test</h2>
    
    <h3>Products Table Test</h3>
    <div id="products-test" class="content-panel">
      <!-- Products table will be inserted here -->
    </div>
    
    <h3>Claim Chart Table Test</h3>
    <div id="claim-chart-test" class="content-panel">
      <!-- Claim chart table will be inserted here -->
    </div>
    
    <button id="test-btn" class="btn btn-primary mt-3">Test Table Formatting</button>
  </div>

  <script>
    // Test data
    const testProductsMarkdown = `| Company | Model | Category | Launch Date | Risk Assessment | Risk Justification |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BRK Brands, Inc. (First Alert) | First Alert Wireless Interconnect Smoke Alarm (SA511B, SM500V) | Smoke Detection | September 2011 | High | All elements of independent claims appear to be met based on official documentation |
| Nest Labs (Google) | Nest Protect Smoke + CO Alarm | Smart Home Safety | October 2013 | Medium | Some claim elements met, requires further investigation |
| Kidde | Wireless Interconnected Smoke Alarm (RF-SM-DC) | Fire Safety | January 2015 | Low | Limited evidence of claim element coverage |`;

    const testClaimChartMarkdown = `| Claim Element | Corresponding Feature in Product | Source Justification |
| :--- | :--- | :--- |
| A wireless network of hazard detection devices | Wireless interconnect smoke alarms that communicate via RF | Official product documentation states "wireless interconnect" capability |
| Each device configured to detect a hazard condition | Smoke detection sensors in each alarm unit | Product specifications confirm smoke detection functionality |
| Generate a human-sensible alarm indication | Audible siren and voice location alerts | User manual describes 85dB siren and voice announcements |`;

    // Enhanced table styling function (copied from script.js)
    function enhanceTableStyling(markdownTable, tableClass = '') {
      if (!markdownTable || !markdownTable.includes('|')) return markdownTable;
      
      const lines = markdownTable.trim().split('\n');
      const tableLines = lines.filter(line => line.includes('|'));
      
      if (tableLines.length < 2) return markdownTable;
      
      // Parse header
      const headerCells = tableLines[0].split('|').map(cell => cell.trim()).filter(cell => cell);
      
      // Skip separator line and parse data rows
      const dataRows = tableLines.slice(2).map(line => 
        line.split('|').map(cell => cell.trim()).filter(cell => cell)
      );
      
      // Build HTML table
      let html = `<table class="table table-striped ${tableClass}">`;
      
      // Add header
      html += '<thead><tr>';
      headerCells.forEach(header => {
        html += `<th>${header}</th>`;
      });
      html += '</tr></thead>';
      
      // Add body
      html += '<tbody>';
      dataRows.forEach(row => {
        html += '<tr>';
        row.forEach((cell, index) => {
          let cellClass = '';
          
          // Add special styling for risk assessment columns
          if (headerCells[index] && headerCells[index].toLowerCase().includes('risk')) {
            if (cell.toLowerCase().includes('high')) cellClass = 'risk-high';
            else if (cell.toLowerCase().includes('medium')) cellClass = 'risk-medium';
            else if (cell.toLowerCase().includes('low')) cellClass = 'risk-low';
          }
          
          // Add column-specific classes for claim charts
          if (tableClass.includes('claim-chart')) {
            if (index === 0) cellClass += ' claim-element-col';
            else if (index === 1) cellClass += ' feature-col';
            else if (index === 2) cellClass += ' justification-col';
          }
          
          html += `<td class="${cellClass}">${cell}</td>`;
        });
        html += '</tr>';
      });
      html += '</tbody></table>';
      
      return html;
    }

    // Test button click handler
    document.getElementById('test-btn').addEventListener('click', function() {
      // Test products table
      const productsHTML = enhanceTableStyling(testProductsMarkdown, 'products-table');
      document.getElementById('products-test').innerHTML = productsHTML;
      
      // Test claim chart table
      const claimChartHTML = enhanceTableStyling(testClaimChartMarkdown, 'claim-chart-table');
      document.getElementById('claim-chart-test').innerHTML = claimChartHTML;
    });
  </script>
</body>
</html>
