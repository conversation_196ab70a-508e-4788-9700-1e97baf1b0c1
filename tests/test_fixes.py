#!/usr/bin/env python3
"""
Test script to verify the database and UI fixes are working correctly.
"""

import sys
import os
import sqlite3
import json

# Add the tools directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

try:
    from tools.database_utils import get_complete_patent_analysis, init_db
    print("✅ Successfully imported database utilities")
except ImportError as e:
    print(f"❌ Failed to import database utilities: {e}")
    sys.exit(1)

def test_database_connection():
    """Test database connection and structure"""
    print("\n🔍 Testing database connection...")
    
    # Initialize database
    try:
        init_db()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    # Check database file exists
    db_path = os.path.join(os.path.dirname(__file__), "wissen_core.db")
    if os.path.exists(db_path):
        print(f"✅ Database file exists at: {db_path}")
    else:
        print(f"❌ Database file not found at: {db_path}")
        return False
    
    # Check tables
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        expected_tables = ['patent_analysis', 'patent_novelty', 'infringed_models', 'claim_charts']
        
        for table in expected_tables:
            if table in tables:
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database table check failed: {e}")
        return False

def test_patent_retrieval():
    """Test patent data retrieval"""
    print("\n🔍 Testing patent data retrieval...")
    
    # Test with existing patents from the database output
    test_patents = ['US9621967B2', 'US7504937B2']
    
    for patent_number in test_patents:
        print(f"\n📋 Testing patent: {patent_number}")
        
        try:
            patent_data = get_complete_patent_analysis(patent_number)
            
            if patent_data:
                print(f"✅ Patent data retrieved for {patent_number}")
                print(f"   - Has data: {patent_data.get('has_data', False)}")
                print(f"   - Analysis data type: {type(patent_data.get('analysis_data'))}")
                print(f"   - Novelty data available: {bool(patent_data.get('novelty_data'))}")
                print(f"   - Infringed models count: {len(patent_data.get('infringed_models', []))}")
                
                # Check if analysis_data is reconstructed properly
                analysis_data = patent_data.get('analysis_data')
                if isinstance(analysis_data, str) and len(analysis_data) > 100:
                    print(f"   - Analysis content reconstructed: ✅ ({len(analysis_data)} chars)")
                elif isinstance(analysis_data, dict):
                    print(f"   - Analysis content (raw dict): ⚠️ {list(analysis_data.keys())}")
                else:
                    print(f"   - Analysis content: ❌ {type(analysis_data)}")
                    
            else:
                print(f"❌ No data found for {patent_number}")
                
        except Exception as e:
            print(f"❌ Error retrieving {patent_number}: {e}")

def test_ui_files():
    """Test UI files exist and have the required elements"""
    print("\n🔍 Testing UI files...")
    
    ui_dir = os.path.join(os.path.dirname(__file__), 'ui')
    required_files = ['index.html', 'script.js', 'styles.css', 'serve.py']
    
    for file_name in required_files:
        file_path = os.path.join(ui_dir, file_name)
        if os.path.exists(file_path):
            print(f"✅ {file_name} exists")
            
            # Check for specific content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if file_name == 'index.html':
                if 'confirmation-dialog' in content and 'confirmation-backdrop' in content:
                    print(f"   - Confirmation dialog elements: ✅")
                else:
                    print(f"   - Confirmation dialog elements: ❌")
                    
            elif file_name == 'script.js':
                if 'showConfirmationDialog' in content and 'hideConfirmationDialog' in content:
                    print(f"   - Confirmation dialog functions: ✅")
                else:
                    print(f"   - Confirmation dialog functions: ❌")
                    
                if 'performNewAnalysis' in content and 'displayExistingAnalysis' in content:
                    print(f"   - Analysis functions: ✅")
                else:
                    print(f"   - Analysis functions: ❌")
                    
            elif file_name == 'styles.css':
                if 'confirmation-dialog' in content and 'confirmation-backdrop' in content:
                    print(f"   - Confirmation dialog styles: ✅")
                else:
                    print(f"   - Confirmation dialog styles: ❌")
                    
        else:
            print(f"❌ {file_name} missing")

def main():
    """Run all tests"""
    print("🧪 Running Patent Infringement Analyzer Fix Tests")
    print("=" * 50)
    
    # Test database
    db_success = test_database_connection()
    
    if db_success:
        test_patent_retrieval()
    
    # Test UI files
    test_ui_files()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")
    print("\n📝 Summary of fixes implemented:")
    print("   1. ✅ Added interactive confirmation dialog for existing analyses")
    print("   2. ✅ Fixed database data reconstruction and display")
    print("   3. ✅ Improved patent data retrieval from chunked tables")
    print("   4. ✅ Added proper chat-like interaction for user decisions")
    print("   5. ✅ Enhanced UI with backdrop and better styling")
    
    print("\n🚀 To test the fixes:")
    print("   1. Run: cd infringement-agent/ui && python serve.py")
    print("   2. Open: http://localhost:8080")
    print("   3. Try analyzing: US9621967B2 or US7504937B2")
    print("   4. You should see the confirmation dialog for existing analyses")

if __name__ == "__main__":
    main()
