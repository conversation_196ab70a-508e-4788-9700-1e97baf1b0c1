#!/usr/bin/env python3
"""
Check what's actually in the database
"""

import sqlite3
import os
import json

def check_database():
    db_path = os.path.join('../infringement-agent', 'wissen_core.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check patent_analysis table
    print("=== Patent Analysis Table ===")
    cursor.execute("SELECT * FROM patent_analysis")
    patent_analyses = cursor.fetchall()
    print(f"Records: {len(patent_analyses)}")
    for row in patent_analyses:
        print(f"  Patent: {row[0]}, Created: {row[2]}")
    
    # Check infringed_models table
    print("\n=== Infringed Models Table ===")
    cursor.execute("SELECT * FROM infringed_models")
    models = cursor.fetchall()
    print(f"Records: {len(models)}")
    for row in models:
        print(f"  ID: {row[0]}, Patent: {row[1]}, Model: {row[3]}")
    
    # Check claim_charts table
    print("\n=== Claim Charts Table ===")
    cursor.execute("SELECT * FROM claim_charts")
    charts = cursor.fetchall()
    print(f"Records: {len(charts)}")
    for row in charts:
        print(f"  ID: {row[0]}, Model ID: {row[1]}, Claim: {row[2][:50]}...")
    
    # Get unique patent numbers from infringed_models
    print("\n=== Unique Patents in Infringed Models ===")
    cursor.execute("SELECT DISTINCT patent_number FROM infringed_models")
    unique_patents = cursor.fetchall()
    for row in unique_patents:
        print(f"  Patent: {row[0]}")
    
    conn.close()
    
    return [row[0] for row in unique_patents] if unique_patents else []

if __name__ == "__main__":
    patents = check_database()
    print(f"\nFound patents: {patents}")
