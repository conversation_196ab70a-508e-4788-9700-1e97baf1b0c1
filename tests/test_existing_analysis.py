#!/usr/bin/env python3
"""
Test script to verify the "Use Existing Analysis" functionality works correctly
"""

import requests
import json
import time

def test_check_patent_endpoint():
    """Test the check patent endpoint"""
    print("Testing check patent endpoint...")
    
    try:
        response = requests.post('http://localhost:8080/api/check-patent', 
                               headers={'Content-Type': 'application/json'},
                               json={'patent_number': 'US11228184B2'})
        
        if response.ok:
            data = response.json()
            print(f"✅ Check patent response: {data}")
            return data.get('exists', False)
        else:
            print(f"❌ Check patent failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing check patent: {e}")
        return False

def test_get_patent_endpoint():
    """Test the get patent endpoint"""
    print("Testing get patent endpoint...")
    
    try:
        response = requests.post('http://localhost:8080/api/get-patent', 
                               headers={'Content-Type': 'application/json'},
                               json={'patent_number': 'US11228184B2'})
        
        if response.ok:
            data = response.json()
            print(f"✅ Get patent response keys: {list(data.keys())}")
            print(f"   Has data: {data.get('has_data', False)}")
            print(f"   Infringed models count: {len(data.get('infringed_models', []))}")
            print(f"   Analysis data length: {len(data.get('analysis_data', ''))}")
            
            # Check if infringed models have claim charts
            if data.get('infringed_models'):
                for i, model in enumerate(data['infringed_models']):
                    claim_charts = model.get('claim_charts', [])
                    print(f"   Model {i+1} ({model.get('model', 'Unknown')}): {len(claim_charts)} claim charts")
            
            return data
        else:
            print(f"❌ Get patent failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error testing get patent: {e}")
        return None

def test_ui_loading():
    """Test that the UI loads correctly"""
    print("Testing UI loading...")
    
    try:
        response = requests.get('http://localhost:8080/')
        
        if response.ok:
            print("✅ UI loads successfully")
            return True
        else:
            print(f"❌ UI loading failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing UI: {e}")
        return False

def main():
    print("Testing 'Use Existing Analysis' functionality...")
    print("=" * 60)
    
    # Test UI loading
    ui_works = test_ui_loading()
    
    # Test database endpoints
    patent_exists = test_check_patent_endpoint()
    patent_data = test_get_patent_endpoint()
    
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"  UI loads: {ui_works}")
    print(f"  Patent exists in DB: {patent_exists}")
    print(f"  Patent data retrievable: {patent_data is not None}")
    
    if patent_data:
        print(f"  Data has meaningful content: {patent_data.get('has_data', False)}")
        print(f"  Ready for 'Use Existing Analysis': {patent_data.get('has_data', False)}")
    
    print("\nNext steps:")
    print("1. Open http://localhost:8080 in your browser")
    print("2. Enter patent number: US11228184B2")
    print("3. Click 'Analyze'")
    print("4. When prompted, click 'No, Use Existing Analysis'")
    print("5. Verify that:")
    print("   - Loading indicator appears with 'Retrieving data from database...'")
    print("   - Confirmation dialog closes")
    print("   - Data displays in all tabs (Summary, Products, Claim Charts)")
    print("   - Loading indicator disappears when done")

if __name__ == "__main__":
    main()
