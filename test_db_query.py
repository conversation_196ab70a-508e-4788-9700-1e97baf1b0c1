#!/usr/bin/env python3
import sys
import os
sys.path.append('infringement-agent')
from tools.database_utils import get_complete_patent_analysis

# Test the function that's being called
result = get_complete_patent_analysis('US9792409B2')
print('=== get_complete_patent_analysis result ===')
print(f'Type: {type(result)}')
if isinstance(result, dict):
    print(f'Keys: {list(result.keys())}')
    print(f'Has data: {result.get("has_data", False)}')
    print(f'Analysis data: {result.get("analysis_data", "None")}')
    print(f'Infringed models count: {len(result.get("infringed_models", []))}')
else:
    print('Result is not a dict')
