#!/usr/bin/env python3
"""
Debug script to check what the API is actually returning
"""

import requests
import json

def debug_api_response():
    """Debug the get-patent API response"""
    patent_number = "US9792409B2"
    
    print(f"Debugging get-patent API response for {patent_number}...")
    
    try:
        response = requests.post(
            'http://localhost:8080/api/get-patent',
            headers={'Content-Type': 'application/json'},
            json={'patent_number': patent_number}
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("📋 Full API Response Structure:")
            print(json.dumps(data, indent=2, default=str))
            
            print("\n🔍 Detailed Analysis:")
            print(f"  Response type: {type(data)}")
            print(f"  Top-level keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if 'infringed_models' in data:
                models = data['infringed_models']
                print(f"  Infringed models type: {type(models)}")
                print(f"  Infringed models count: {len(models) if isinstance(models, list) else 'Not a list'}")
                
                if isinstance(models, list) and len(models) > 0:
                    first_model = models[0]
                    print(f"  First model type: {type(first_model)}")
                    print(f"  First model keys: {list(first_model.keys()) if isinstance(first_model, dict) else 'Not a dict'}")
                    
                    # Check each field specifically
                    fields_to_check = ['id', 'model', 'company', 'category', 'launched_date', 'activity_date', 'risk_assessment', 'risk_justification']
                    print(f"\n  📊 Field-by-field analysis:")
                    for field in fields_to_check:
                        if isinstance(first_model, dict):
                            value = first_model.get(field, 'MISSING')
                            print(f"    {field}: {value} (type: {type(value)})")
                        else:
                            print(f"    {field}: Cannot check - model is not a dict")
            
        else:
            print(f"❌ API request failed with status: {response.status_code}")
            print(f"Response text: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during API request: {e}")

if __name__ == "__main__":
    debug_api_response()
