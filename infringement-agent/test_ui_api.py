#!/usr/bin/env python3
"""
Test script to verify UI API endpoints work correctly
"""

import requests
import json

def test_check_patent_api():
    """Test the check-patent API endpoint"""
    patent_number = "US9792409B2"
    
    print(f"Testing check-patent API for {patent_number}...")
    
    try:
        response = requests.post(
            'http://localhost:8080/api/check-patent',
            headers={'Content-Type': 'application/json'},
            json={'patent_number': patent_number}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✓ Check-patent API successful")
            print(f"  Exists: {data.get('exists', False)}")
            print(f"  Patent Number: {data.get('patent_number', 'NOT FOUND')}")
            return data.get('exists', False)
        else:
            print(f"✗ Check-patent API failed with status: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Check-patent API error: {e}")
        return False

def test_get_patent_api():
    """Test the get-patent API endpoint"""
    patent_number = "US9792409B2"
    
    print(f"\nTesting get-patent API for {patent_number}...")
    
    try:
        response = requests.post(
            'http://localhost:8080/api/get-patent',
            headers={'Content-Type': 'application/json'},
            json={'patent_number': patent_number}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✓ Get-patent API successful")
            print(f"  Has data: {data.get('has_data', False)}")
            print(f"  Patent Number: {data.get('patent_number', 'NOT FOUND')}")
            print(f"  Analysis data length: {len(data.get('analysis_data', '')) if data.get('analysis_data') else 0}")
            print(f"  Infringed models count: {len(data.get('infringed_models', []))}")
            
            # Check if infringed models have the new fields
            models = data.get('infringed_models', [])
            if models:
                print("\n📋 First model fields:")
                model = models[0]
                required_fields = ['company', 'model', 'category', 'launched_date', 'activity_date', 'risk_assessment', 'risk_justification']
                for field in required_fields:
                    value = model.get(field, 'NOT FOUND')
                    print(f"    {field}: {value}")
                    
                # Check if all required fields are present
                missing_fields = [field for field in required_fields if field not in model or model[field] is None]
                if missing_fields:
                    print(f"  ⚠️  Missing fields: {missing_fields}")
                else:
                    print("  ✓ All required fields present")
            
            return True
        else:
            print(f"✗ Get-patent API failed with status: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Get-patent API error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing UI API endpoints...\n")
    
    check_success = test_check_patent_api()
    get_success = test_get_patent_api()
    
    if check_success and get_success:
        print("\n🎉 All UI API tests PASSED!")
    else:
        print("\n❌ Some UI API tests FAILED!")
