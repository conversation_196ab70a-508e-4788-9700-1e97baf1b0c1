#!/usr/bin/env python3
"""
Test script to verify database migration and new field functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.database_utils import init_db, save_infringed_model, get_infringed_models

def test_database_migration():
    """Test that database migration works correctly"""
    print("Testing database migration...")
    
    # Initialize database (this will run migration)
    init_db()
    print("✓ Database initialized with migration")
    
    # Test saving a model with new fields
    test_patent = "US9792409B2"
    test_model_data = {
        'model': 'Test Product Model',
        'company': 'Test Company Inc.',
        'category': 'Electronics',
        'launched_date': '2023-01-15',
        'activity_date': '2024-01-01',
        'risk_assessment': 'High',
        'risk_justification': 'Multiple claim elements match product features',
        'link': 'https://example.com/product',
        'is_relevant': 1,
        'relevance_reason': 'Direct feature match'
    }
    
    print(f"Saving test model data for patent {test_patent}...")
    model_id = save_infringed_model(test_patent, test_model_data)
    
    if model_id:
        print(f"✓ Model saved successfully with ID: {model_id}")
    else:
        print("✗ Failed to save model")
        return False
    
    # Test retrieving the model
    print("Retrieving saved model data...")
    models = get_infringed_models(test_patent)
    
    if models and len(models) > 0:
        model = models[0]
        print("✓ Model retrieved successfully")
        print(f"  Company: {model.get('company', 'NOT FOUND')}")
        print(f"  Model: {model.get('model', 'NOT FOUND')}")
        print(f"  Category: {model.get('category', 'NOT FOUND')}")
        print(f"  Launch Date: {model.get('launched_date', 'NOT FOUND')}")
        print(f"  Activity Date: {model.get('activity_date', 'NOT FOUND')}")
        print(f"  Risk Assessment: {model.get('risk_assessment', 'NOT FOUND')}")
        print(f"  Risk Justification: {model.get('risk_justification', 'NOT FOUND')}")
        
        # Verify all new fields are present
        required_fields = ['company', 'category', 'activity_date', 'risk_assessment', 'risk_justification']
        missing_fields = [field for field in required_fields if field not in model or model[field] is None]
        
        if missing_fields:
            print(f"✗ Missing fields: {missing_fields}")
            return False
        else:
            print("✓ All required fields are present and populated")
            return True
    else:
        print("✗ Failed to retrieve model")
        return False

if __name__ == "__main__":
    success = test_database_migration()
    if success:
        print("\n🎉 Database migration test PASSED!")
    else:
        print("\n❌ Database migration test FAILED!")
        sys.exit(1)
