#!/usr/bin/env python3
"""
Test script to check existing patent data for US9792409B2
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.database_utils import get_complete_patent_analysis, get_infringed_models

def test_patent_data():
    """Test retrieving patent data for US9792409B2"""
    patent_number = "US9792409B2"
    
    print(f"Testing patent data retrieval for {patent_number}...")
    
    # Get complete analysis
    complete_data = get_complete_patent_analysis(patent_number)
    
    if complete_data:
        print("✓ Complete patent data retrieved")
        print(f"  Has data: {complete_data.get('has_data', False)}")
        print(f"  Patent number: {complete_data.get('patent_number', 'NOT FOUND')}")
        print(f"  Analysis data length: {len(complete_data.get('analysis_data', '')) if complete_data.get('analysis_data') else 0}")
        print(f"  Infringed models count: {len(complete_data.get('infringed_models', []))}")
        
        # Check infringed models
        models = complete_data.get('infringed_models', [])
        if models:
            print("\n📋 Infringed Models:")
            for i, model in enumerate(models, 1):
                print(f"  Model {i}:")
                print(f"    Company: {model.get('company', 'NOT SET')}")
                print(f"    Model: {model.get('model', 'NOT SET')}")
                print(f"    Category: {model.get('category', 'NOT SET')}")
                print(f"    Launch Date: {model.get('launched_date', 'NOT SET')}")
                print(f"    Activity Date: {model.get('activity_date', 'NOT SET')}")
                print(f"    Risk Assessment: {model.get('risk_assessment', 'NOT SET')}")
                print(f"    Risk Justification: {model.get('risk_justification', 'NOT SET')}")
                print(f"    Claim Charts: {len(model.get('claim_charts', []))} items")
        else:
            print("\n📋 No infringed models found")
            
        # Check novelty data
        novelty_data = complete_data.get('novelty_data', {})
        if novelty_data:
            print(f"\n📄 Novelty Data:")
            print(f"  Title: {novelty_data.get('title', 'NOT SET')}")
            print(f"  Priority Date: {novelty_data.get('priority_date', 'NOT SET')}")
            print(f"  Assignees: {novelty_data.get('assignees', 'NOT SET')}")
        else:
            print("\n📄 No novelty data found")
            
    else:
        print("✗ No patent data found")
        
    # Also test direct model retrieval
    print(f"\n🔍 Direct model retrieval test:")
    models = get_infringed_models(patent_number)
    print(f"  Found {len(models)} models directly")
    
    return complete_data is not None

if __name__ == "__main__":
    success = test_patent_data()
    if success:
        print("\n🎉 Patent data test completed!")
    else:
        print("\n❌ Patent data test failed!")
