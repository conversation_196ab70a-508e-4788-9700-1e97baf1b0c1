#!/usr/bin/env python3
"""
Setup realistic test data for US9792409B2 patent
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.database_utils import save_infringed_model, save_claim_chart
import sqlite3

def clear_existing_data():
    """Clear existing test data for US9792409B2"""
    patent_number = "US9792409B2"
    
    # Connect to database
    db_file = os.path.join(os.path.dirname(__file__), "wissen_core.db")
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # Delete existing models and claim charts for this patent
    cursor.execute("DELETE FROM claim_charts WHERE infringed_model_id IN (SELECT id FROM infringed_models WHERE patent_number = ?)", (patent_number,))
    cursor.execute("DELETE FROM infringed_models WHERE patent_number = ?", (patent_number,))
    
    conn.commit()
    conn.close()
    print(f"✓ Cleared existing data for {patent_number}")

def setup_realistic_data():
    """Setup realistic test data for US9792409B2"""
    patent_number = "US9792409B2"
    
    # Clear existing data first
    clear_existing_data()
    
    # Create realistic infringed models
    models_data = [
        {
            'model': 'HidrateSpark PRO',
            'company': 'Hidrate Inc.',
            'category': 'Smart Water Bottles',
            'launched_date': '2019-06-15',
            'activity_date': '2023-12-01',
            'risk_assessment': 'High',
            'risk_justification': 'Contains smart tracking, mobile app integration, and hydration reminders matching patent claims',
            'link': 'https://hidratespark.com/products/hidrate-spark-pro',
            'is_relevant': 1,
            'relevance_reason': 'Direct feature match with patent claims for smart water bottle with tracking'
        },
        {
            'model': 'Thermos Smart Lid',
            'company': 'Thermos LLC',
            'category': 'Smart Drinkware',
            'launched_date': '2020-03-10',
            'activity_date': '2024-01-15',
            'risk_assessment': 'Medium',
            'risk_justification': 'Has temperature tracking and mobile connectivity but lacks AI-driven behavior prediction',
            'link': 'https://www.thermos.com/smart-lid',
            'is_relevant': 1,
            'relevance_reason': 'Partial feature match - smart connectivity but limited AI functionality'
        },
        {
            'model': 'Cirkul Smart Bottle',
            'company': 'Cirkul Inc.',
            'category': 'Smart Hydration',
            'launched_date': '2021-09-20',
            'activity_date': '2023-11-30',
            'risk_assessment': 'Low',
            'risk_justification': 'Focuses on flavor customization rather than activity tracking and AI predictions',
            'link': 'https://drinkcirkul.com/smart-bottle',
            'is_relevant': 0,
            'relevance_reason': 'Different focus area - flavor customization vs activity tracking'
        }
    ]
    
    print(f"Setting up realistic test data for {patent_number}...")
    
    for i, model_data in enumerate(models_data, 1):
        print(f"  Adding model {i}: {model_data['model']}")
        model_id = save_infringed_model(patent_number, model_data)
        
        if model_id:
            # Add claim charts for high and medium risk models
            if model_data['risk_assessment'] in ['High', 'Medium']:
                claim_charts = []
                
                if model_data['risk_assessment'] == 'High':
                    claim_charts = [
                        {
                            'claim_element': 'A water bottle with electronic components',
                            'corresponding_feature': 'Smart water bottle with Bluetooth connectivity and sensors',
                            'source_justification': 'Product specifications clearly show electronic components integrated into bottle design',
                            'infringement_risk': 'High',
                            'risk_justification': 'Direct implementation of claimed electronic water bottle'
                        },
                        {
                            'claim_element': 'Activity tracking and hydration monitoring',
                            'corresponding_feature': 'Mobile app tracks water intake and provides hydration goals',
                            'source_justification': 'App store description and user manual detail activity tracking features',
                            'infringement_risk': 'High',
                            'risk_justification': 'Implements core patent functionality for activity-based hydration'
                        },
                        {
                            'claim_element': 'AI-driven behavior prediction',
                            'corresponding_feature': 'Smart reminders based on user patterns and activity',
                            'source_justification': 'Marketing materials mention "intelligent reminders" and "learning algorithms"',
                            'infringement_risk': 'Medium',
                            'risk_justification': 'Similar functionality but implementation details may differ'
                        }
                    ]
                elif model_data['risk_assessment'] == 'Medium':
                    claim_charts = [
                        {
                            'claim_element': 'A water bottle with electronic components',
                            'corresponding_feature': 'Temperature sensor and Bluetooth connectivity in lid',
                            'source_justification': 'Product manual shows electronic components in smart lid design',
                            'infringement_risk': 'Medium',
                            'risk_justification': 'Partial implementation - electronics in lid rather than bottle body'
                        },
                        {
                            'claim_element': 'Mobile device connectivity',
                            'corresponding_feature': 'Bluetooth connection to smartphone app',
                            'source_justification': 'App store listing and technical specifications confirm Bluetooth connectivity',
                            'infringement_risk': 'High',
                            'risk_justification': 'Direct implementation of mobile connectivity claim'
                        }
                    ]
                
                if claim_charts:
                    save_claim_chart(model_id, claim_charts)
                    print(f"    Added {len(claim_charts)} claim chart entries")
        else:
            print(f"    ❌ Failed to save model: {model_data['model']}")
    
    print(f"✓ Realistic test data setup complete for {patent_number}")

if __name__ == "__main__":
    setup_realistic_data()
    print("\n🎉 Test data setup completed successfully!")
